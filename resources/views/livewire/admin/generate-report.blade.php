<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Commission Report Management</h3>
                    <div class="card-tools">
                        <ul class="ml-auto nav nav-pills">
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'generate' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('generate')">
                                    <i class="fas fa-calculator me-2"></i>Generate Report
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'preview' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('preview')">
                                    <i class="fas fa-eye me-2"></i>Preview Sales
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'sales' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('sales')">
                                    <i class="fas fa-list me-2"></i>Sales List
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'history' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('history')">
                                    <i class="fas fa-history me-2"></i>History
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {{-- Flash Messages --}}
                    @if (session()->has('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session()->has('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    {{-- Generate Tab Content --}}
                    @if ($activeTab === 'generate')

                    {{-- Generate Buttons --}}
                    @if (!$isSubmitted)
                        <div class="mb-4 text-center">
                            <div class="btn-group" role="group">
                                <button wire:click="previewSales" class="btn btn-outline-info btn-lg" wire:loading.attr="disabled">
                                    <span wire:loading.remove wire:target="previewSales">
                                        <i class="fas fa-eye me-2"></i>Preview Sales & Commission
                                    </span>
                                    <span wire:loading wire:target="previewSales">
                                        <i class="fas fa-spinner fa-spin me-2"></i>Loading...
                                    </span>
                                </button>
                                <button wire:click="generate" class="btn btn-primary btn-lg" wire:loading.attr="disabled">
                                    <span wire:loading.remove wire:target="generate">
                                        <i class="fas fa-calculator me-2"></i>Generate Commission Report
                                    </span>
                                    <span wire:loading wire:target="generate">
                                        <i class="fas fa-spinner fa-spin me-2"></i>Generating...
                                    </span>
                                </button>
                            </div>
                            <p class="mt-2 text-muted">Preview sales first to see potential commissions, then generate the final report</p>
                        </div>
                    @else
                        <div class="text-center alert alert-info">
                            <i class="fas fa-check-circle me-2"></i>
                            Commission report has already been generated and submitted.
                        </div>
                    @endif

                    {{-- Payout Summary Cards --}}
                    @if ($showResults)
                        <div class="mb-4 row">
                            <div class="col-md-6">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Main Agent Payout</h5>
                                                <h3 class="mb-0">RM {{ $totalMainAgentPayout }}</h3>
                                                <small class="opacity-75">40% Commission Split</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-user-tie fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Agent Payout</h5>
                                                <h3 class="mb-0">RM {{ $totalAgentPayout }}</h3>
                                                <small class="opacity-75">60% Commission Split</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-users fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    {{-- Commission Reports Table for Generate Tab --}}
                    @if ($showResults && count($commissionReports) > 0)
                        <div class="mt-4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Commission Report Details</h3>
                                    <div class="card-tools">
                                        <span class="badge bg-info">{{ count($commissionReports) }} Records</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>User ID</th>
                                                    <th>User Name</th>
                                                    <th>Cooperative ID</th>
                                                    <th>Variety Type</th>
                                                    <th>Quantity</th>
                                                    <th>Total Price</th>
                                                    <th>Commission %</th>
                                                    <th>Total Commission</th>
                                                    <th>Main Agent (40%)</th>
                                                    <th>Agent (60%)</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($commissionReports as $report)
                                                    <tr>
                                                        <td><code>{{ $report['acs_user_id'] }}</code></td>
                                                        <td>{{ $report['user_name'] }}</td>
                                                        <td>{{ $report['cooperative_id'] }}</td>
                                                        <td>
                                                            <span class="badge bg-secondary">{{ $report['variety_type'] }}</span>
                                                        </td>
                                                        <td class="text-center">{{ $report['quantity'] }}</td>
                                                        <td class="text-end">RM {{ number_format($report['total_price'], 2) }}</td>
                                                        <td class="text-center">{{ $report['commission_percentage'] }}%</td>
                                                        <td class="text-end">RM {{ $report['total_commissions'] }}</td>
                                                        <td class="text-end">RM {{ $report['master_agent_commission'] }}</td>
                                                        <td class="text-end">RM {{ $report['agent_commission'] }}</td>
                                                        <td class="text-center">
                                                            @if ($report['saved_to_db'])
                                                                <span class="badge bg-success">
                                                                    <i class="fas fa-check me-1"></i>Saved
                                                                </span>
                                                            @else
                                                                <span class="badge bg-warning">
                                                                    <i class="fas fa-exclamation me-1"></i>No Tier
                                                                </span>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @endif {{-- End Generate Tab --}}

                    {{-- Preview Tab Content --}}
                    @if ($activeTab === 'preview')
                        @if ($showPreview && isset($previewData['sales']))
                            {{-- Preview Summary Cards --}}
                            <div class="mb-4 row">
                                <div class="col-md-3">
                                    <div class="text-white card bg-info">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h5 class="card-title">Total Sales</h5>
                                                    <h3 class="mb-0">{{ $previewData['summary']['total_sales'] }}</h3>
                                                    <small class="opacity-75">Unprocessed Records</small>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="opacity-50 fas fa-shopping-cart fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-white card bg-success">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h5 class="card-title">Potential Commission</h5>
                                                    <h3 class="mb-0">RM {{ number_format($previewData['summary']['total_potential_commission'], 2) }}</h3>
                                                    <small class="opacity-75">Total Expected</small>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="opacity-50 fas fa-dollar-sign fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-white card bg-primary">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h5 class="card-title">Main Agent (40%)</h5>
                                                    <h3 class="mb-0">RM {{ number_format($previewData['summary']['total_main_agent_payout'], 2) }}</h3>
                                                    <small class="opacity-75">Expected Payout</small>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="opacity-50 fas fa-user-tie fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-white card bg-warning">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    <h5 class="card-title">Agent (60%)</h5>
                                                    <h3 class="mb-0">RM {{ number_format($previewData['summary']['total_agent_payout'], 2) }}</h3>
                                                    <small class="opacity-75">Expected Payout</small>
                                                </div>
                                                <div class="align-self-center">
                                                    <i class="opacity-50 fas fa-users fa-3x"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- Sales Preview Table --}}
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Sales Preview & Commission Calculation</h3>
                                    <div class="card-tools">
                                        <span class="badge bg-info">{{ count($previewData['sales']) }} Sales Records</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped table-sm">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>Sale ID</th>
                                                    <th>User</th>
                                                    <th>Variety</th>
                                                    <th>SKU</th>
                                                    <th>Price</th>
                                                    <th>Qty for Tier</th>
                                                    <th>Commission %</th>
                                                    <th>Total Commission</th>
                                                    <th>Main Agent</th>
                                                    <th>Agent</th>
                                                    <th>Status</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($previewData['sales'] as $sale)
                                                    <tr>
                                                        <td><code class="small">{{ substr($sale['sale_id'], 0, 8) }}...</code></td>
                                                        <td>
                                                            <div class="small">
                                                                <strong>{{ $sale['user_name'] }}</strong><br>
                                                                <span class="text-muted">{{ $sale['user_email'] }}</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary">{{ $sale['variety_type'] }}</span>
                                                        </td>
                                                        <td><code class="small">{{ $sale['sku_number'] }}</code></td>
                                                        <td class="text-end">RM {{ number_format($sale['total_price'], 2) }}</td>
                                                        <td class="text-center">{{ $sale['quantity_for_tier'] }}</td>
                                                        <td class="text-center">{{ $sale['commission_percentage'] }}%</td>
                                                        <td class="text-end">RM {{ number_format($sale['commission_amount'], 2) }}</td>
                                                        <td class="text-end">RM {{ number_format($sale['master_agent_commission'], 2) }}</td>
                                                        <td class="text-end">RM {{ number_format($sale['agent_commission'], 2) }}</td>
                                                        <td class="text-center">
                                                            @if ($sale['tier_found'])
                                                                <span class="badge bg-success">
                                                                    <i class="fas fa-check me-1"></i>Tier Found
                                                                </span>
                                                            @else
                                                                <span class="badge bg-warning">
                                                                    <i class="fas fa-exclamation me-1"></i>No Tier
                                                                </span>
                                                            @endif
                                                        </td>
                                                        <td class="small">{{ \Carbon\Carbon::parse($sale['created_at'])->format('M d, Y') }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="text-muted">
                                            Showing {{ count($previewData['sales']) }} sales records ready for commission processing
                                        </div>
                                        @if (!$isSubmitted)
                                            <button wire:click="generate" class="btn btn-primary">
                                                <i class="fas fa-calculator me-2"></i>Generate Commission Report
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="py-5 text-center">
                                <i class="mb-3 fas fa-eye fa-3x text-muted"></i>
                                <h4>No Preview Available</h4>
                                <p class="text-muted">Click "Preview Sales & Commission" to see unprocessed sales and potential commissions.</p>
                                <button wire:click="previewSales" class="btn btn-primary">
                                    <i class="fas fa-eye me-2"></i>Preview Sales
                                </button>
                            </div>
                        @endif
                    @endif

                    {{-- Sales List Tab Content --}}
                    @if ($activeTab === 'sales')
                        {{-- Sales Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Sales</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['total_sales'] }}</h3>
                                                <small class="opacity-75">All Records</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-shopping-cart fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Processed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['processed_sales'] }}</h3>
                                                <small class="opacity-75">Commission Generated</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-check-circle fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Unprocessed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['unprocessed_sales'] }}</h3>
                                                <small class="opacity-75">Pending Commission</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-clock fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Value</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->salesStats['total_value'], 2) }}</h3>
                                                <small class="opacity-75">All Sales</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="salesFilter" class="form-label">Status Filter</label>
                                        <select class="form-control" id="salesFilter" wire:model.live="salesFilter">
                                            <option value="all">All Sales</option>
                                            <option value="unprocessed">Unprocessed Only</option>
                                            <option value="processed">Processed Only</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by SKU, user, variety...">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="dateFrom" class="form-label">Date From</label>
                                        <input type="date" class="form-control" id="dateFrom"
                                               wire:model.live="dateFrom">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="dateTo" class="form-label">Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateFrom', ''); $set('dateTo', ''); $set('salesFilter', 'all')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Sales List Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Sales Records</h3>
                                <div class="card-tools">
                                    <span class="badge bg-info">{{ $this->salesList->total() }} Total Records</span>
                                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                                        <button wire:click="generate" class="btn btn-primary btn-sm ms-2" wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="generate">
                                                <i class="fas fa-calculator me-1"></i>Generate Report
                                            </span>
                                            <span wire:loading wire:target="generate">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Generating...
                                            </span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>User</th>
                                                <th>Variety</th>
                                                <th>Quantity</th>
                                                <th>Total Price</th>
                                                <th>Status</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Date Range</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->salesList as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">Coop: {{ $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-info">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">
                                                        @if ($sale->status == 1)
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check me-1"></i>Processed
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning">
                                                                <i class="fas fa-clock me-1"></i>Unprocessed
                                                            </span>
                                                            <small class="text-muted d-block">Ready for commission</small>
                                                        @endif
                                                    </td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="small">
                                                        @if ($sale->first_sale_date == $sale->last_sale_date)
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d, Y') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d') }} -
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {{ $this->salesList->links() }}
                                    </div>
                                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                                        <div>
                                            <button wire:click="generate" class="btn btn-primary" wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="generate">
                                                    <i class="fas fa-calculator me-2"></i>Generate Commission Report
                                                </span>
                                                <span wire:loading wire:target="generate">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>Generating...
                                                </span>
                                            </button>
                                            <small class="text-muted d-block mt-1">
                                                {{ $this->salesStats['unprocessed_sales'] }} unprocessed sales ready for commission generation
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    {{-- History Tab Content --}}
                    @if ($activeTab === 'history')
                        {{-- History Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Records</h5>
                                                <h3 class="mb-0">{{ $this->historyStats['total_records'] }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Commission</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_commission'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Main Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_main_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-user-tie fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-users fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by invoice or affiliate number...">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateFrom" class="form-label">Date From</label>
                                        <input type="date" class="form-control" id="dateFrom"
                                               wire:model.live="dateFrom">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateTo" class="form-label">Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateFrom', ''); $set('dateTo', '')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Commission History Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Commission History</h3>
                                <div class="card-tools">
                                    <span class="badge bg-info">{{ $this->commissionHistory->total() }} Total Records</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Invoice No</th>
                                                <th>Affiliate Member</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->commissionHistory as $commission)
                                                <tr>
                                                    <td><code>{{ $commission->invoice_no }}</code></td>
                                                    <td>{{ $commission->affiliate_membership_no }}</td>
                                                    <td class="text-center">{{ $commission->comission_percentage }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission->comission_amount, 2) }}</td>
                                                    <td class="text-end">
                                                        @if($commission->distributions->isNotEmpty())
                                                            RM {{ number_format($commission->distributions->first()->main_agent_commission_amount, 2) }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td class="text-end">
                                                        @if($commission->distributions->isNotEmpty())
                                                            RM {{ number_format($commission->distributions->first()->agent_commission_amount, 2) }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td class="small">{{ \Carbon\Carbon::parse($commission->created_at)->format('M d, Y H:i') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                {{ $this->commissionHistory->links() }}
                            </div>
                        </div>
                    @endif {{-- End History Tab --}}
                </div>
            </div>
        </div>
    </div>
</div>
