<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Sales & Commission Management</h3>
                    <div class="card-tools">
                        <ul class="ml-auto nav nav-pills">
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'sales' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('sales')">
                                    <i class="fas fa-list me-2"></i>Sales List
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'history' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('history')">
                                    <i class="fas fa-history me-2"></i>History
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {{-- Flash Messages --}}
                    @if (session()->has('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session()->has('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif





                    {{-- Sales List Tab Content --}}
                    @if ($activeTab === 'sales')
                        {{-- Sales Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Sales</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['total_sales'] }}</h3>
                                                <small class="opacity-75">All Records</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-shopping-cart fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Processed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['processed_sales'] }}</h3>
                                                <small class="opacity-75">Commission Generated</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-check-circle fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Unprocessed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['unprocessed_sales'] }}</h3>
                                                <small class="opacity-75">Pending Commission</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-clock fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Value</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->salesStats['total_value'], 2) }}</h3>
                                                <small class="opacity-75">All Sales</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="salesFilter" class="form-label">Status Filter</label>
                                        <select class="form-control" id="salesFilter" wire:model.live="salesFilter">
                                            <option value="all">All Sales</option>
                                            <option value="unprocessed">Unprocessed Only</option>
                                            <option value="processed">Processed Only</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by SKU, user, variety...">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="dateFrom" class="form-label">Date From</label>
                                        <input type="date" class="form-control" id="dateFrom"
                                               wire:model.live="dateFrom">
                                    </div>
                                    <div class="col-md-2">
                                        <label for="dateTo" class="form-label">Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateFrom', ''); $set('dateTo', ''); $set('salesFilter', 'all')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Sales List Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Sales Records</h3>
                                <div class="card-tools">
                                    <span class="badge bg-info">{{ $this->salesList->total() }} Total Records</span>
                                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                                        <button wire:click="generate" class="btn btn-primary btn-sm ms-2" wire:loading.attr="disabled">
                                            <span wire:loading.remove wire:target="generate">
                                                <i class="fas fa-calculator me-1"></i>Generate Report
                                            </span>
                                            <span wire:loading wire:target="generate">
                                                <i class="fas fa-spinner fa-spin me-1"></i>Generating...
                                            </span>
                                        </button>
                                    @endif
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>User</th>
                                                <th>Variety</th>
                                                <th>Quantity</th>
                                                <th>Total Price</th>
                                                <th>Status</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Date Range</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->salesList as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">Coop: {{ $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-info">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">
                                                        @if ($sale->status == 1)
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check me-1"></i>Processed
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning">
                                                                <i class="fas fa-clock me-1"></i>Unprocessed
                                                            </span>
                                                            <small class="text-muted d-block">Ready for commission</small>
                                                        @endif
                                                    </td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="small">
                                                        @if ($sale->first_sale_date == $sale->last_sale_date)
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d, Y') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d') }} -
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {{ $this->salesList->links() }}
                                    </div>
                                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                                        <div>
                                            <button wire:click="generate" class="btn btn-primary" wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="generate">
                                                    <i class="fas fa-calculator me-2"></i>Generate Commission Report
                                                </span>
                                                <span wire:loading wire:target="generate">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>Generating...
                                                </span>
                                            </button>
                                            <small class="text-muted d-block mt-1">
                                                {{ $this->salesStats['unprocessed_sales'] }} unprocessed sales ready for commission generation
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    {{-- History Tab Content --}}
                    @if ($activeTab === 'history')
                        {{-- History Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Records</h5>
                                                <h3 class="mb-0">{{ $this->historyStats['total_records'] }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Commission</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_commission'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Main Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_main_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-user-tie fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-users fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by invoice or affiliate number...">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateFrom" class="form-label">Date From</label>
                                        <input type="date" class="form-control" id="dateFrom"
                                               wire:model.live="dateFrom">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateTo" class="form-label">Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateFrom', ''); $set('dateTo', '')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Commission History Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Commission History</h3>
                                <div class="card-tools">
                                    <span class="badge bg-info">{{ $this->commissionHistory->total() }} Total Records</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Invoice No</th>
                                                <th>Affiliate Member</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->commissionHistory as $commission)
                                                <tr>
                                                    <td><code>{{ $commission->invoice_no }}</code></td>
                                                    <td>{{ $commission->affiliate_membership_no }}</td>
                                                    <td class="text-center">{{ $commission->comission_percentage }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission->comission_amount, 2) }}</td>
                                                    <td class="text-end">
                                                        @if($commission->distributions->isNotEmpty())
                                                            RM {{ number_format($commission->distributions->first()->main_agent_commission_amount, 2) }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td class="text-end">
                                                        @if($commission->distributions->isNotEmpty())
                                                            RM {{ number_format($commission->distributions->first()->agent_commission_amount, 2) }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td class="small">{{ \Carbon\Carbon::parse($commission->created_at)->format('M d, Y H:i') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                {{ $this->commissionHistory->links() }}
                            </div>
                        </div>
                    @endif {{-- End History Tab --}}
                </div>
            </div>
        </div>
    </div>
</div>
