<?php

namespace App\Livewire\Admin;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\AcsSales;
use App\Models\AcsCommissionSetting;
use App\Models\AcsSalesCommission;
use App\Models\AcsCommissionDistribution;

class GenerateReport extends Component
{
    use WithPagination;

    public $isSubmitted = false;
    public $commissionReports = [];
    public $totalMainAgentPayout = 0;
    public $totalAgentPayout = 0;
    public $showResults = false;
    public $activeTab = 'generate'; // 'generate', 'preview', 'sales', or 'history'
    public $searchTerm = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $showPreview = false;
    public $previewData = [];
    public $salesFilter = 'all'; // 'all', 'processed', 'unprocessed'

    protected $paginationTheme = 'bootstrap';

    public function mount()
    {
        // Check if commission has already been generated (status = 1 on any acs_sales)
        $this->isSubmitted = DB::table('acs_sales')->where('status', 1)->exists();
    }

    public function render()
    {
        return view('livewire.admin.generate-report');
    }

    public function generate(){
        // Check if already submitted
        if ($this->isSubmitted) {
            session()->flash('error', 'Commission report has already been generated and submitted.');
            return;
        }

        // Get aggregated sales data grouped by user (only status = 0)
        $userSales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->select(
                    'acs_sales.acs_users_id',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(acs_sales.total_price) as total_price'),
                    'acs_sales.variety_type'
                )
                ->groupBy('acs_sales.acs_users_id', 'acs_users.name', 'acs_users.email', 'acs_users.acs_coorperative_id', 'acs_sales.variety_type')
                ->get();

        if ($userSales->isEmpty()) {
            session()->flash('error', 'No unprocessed sales found to generate commission report.');
            return;
        }

        $commissionReports = [];
        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;

        foreach($userSales as $userSale){
            // Find the commission tier for this user's total quantity
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $userSale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $userSale->quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $userSale->quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty',
                        'acs_comission_settings.variety_type'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
            }

            // Commission distribution calculations (40/60 split)
            $mainAgentPercentage = 40;
            $agentPercentage = 60;
            $totalCommissions = number_format($commissionAmount, 2, '.', '');
            $agentCommission = number_format(60 / 100 * $commissionAmount, 2, '.', '');
            $masterAgentCommission = number_format(40 / 100 * $commissionAmount, 2, '.', '');

            // Save to database if commission found
            if ($tierCommission && $commissionAmount > 0) {
                DB::beginTransaction();
                try {
                    // Create sales commission record
                    $salesCommission = AcsSalesCommission::create([
                        'table_22_jualan_id' => 1, // You may need to adjust this
                        'table_23_senarai_jualan_id' => 1, // You may need to adjust this
                        'invoice_no' => 'INV-' . time() . '-' . $userSale->acs_users_id,
                        'affiliate_membership_no' => $userSale->acs_users_id,
                        'senarai_pelanggan_id' => 1, // You may need to adjust this
                        'comission_percentage' => $commissionPercentage,
                        'comission_amount' => $commissionAmount,
                    ]);

                    // Get user details for commission distribution
                    $userDetails = DB::table('acs_users')
                        ->where('id', $userSale->acs_users_id)
                        ->first();

                    // Create commission distribution record
                    AcsCommissionDistribution::create([
                        'acs_sales_commission_id' => $salesCommission->id,
                        'acs_coorperative_id' => $userSale->acs_coorperative_id,
                        'acs_coorperative_branch_id' => $userDetails->acs_coorperative_branch_id ?? 1,
                        'acs_main_agent_user_id' => $userDetails->invited_by ?? $userSale->acs_users_id,
                        'main_agent_percentage' => $mainAgentPercentage,
                        'main_agent_commission_amount' => $masterAgentCommission,
                        'agent_percentage' => $agentPercentage,
                        'agent_commission_amount' => $agentCommission,
                        'acs_agent_user_id' => $userSale->acs_users_id,
                    ]);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                    // Log error for debugging
                    Log::error('Commission distribution save failed: ' . $e->getMessage());
                }
            }

            // Accumulate payout totals
            $totalMainAgentPayout += floatval($masterAgentCommission);
            $totalAgentPayout += floatval($agentCommission);

            // Add to commission reports
            $commissionReports[] = [
                'acs_user_id' => $userSale->acs_users_id,
                'user_name' => $userSale->user_name,
                'user_email' => $userSale->user_email,
                'cooperative_id' => $userSale->acs_coorperative_id,
                'quantity' => $userSale->quantity,
                'total_price' => $userSale->total_price,
                'variety_type' => $userSale->variety_type,
                'commission_percentage' => $commissionPercentage,
                'commission_amount' => $commissionAmount,
                'main_agent_percentage' => $mainAgentPercentage,
                'agent_percentage' => $agentPercentage,
                'total_commissions' => $totalCommissions,
                'agent_commission' => $agentCommission,
                'master_agent_commission' => $masterAgentCommission,
                'tier_found' => $tierCommission ? true : false,
                'saved_to_db' => ($tierCommission && $commissionAmount > 0) ? true : false
            ];
        }

        // Update all processed sales status to 1
        DB::table('acs_sales')
            ->where('status', 0)
            ->update(['status' => 1]);

        // Set component properties
        $this->commissionReports = $commissionReports;
        $this->totalMainAgentPayout = number_format($totalMainAgentPayout, 2, '.', '');
        $this->totalAgentPayout = number_format($totalAgentPayout, 2, '.', '');
        $this->isSubmitted = true;
        $this->showResults = true;

        // Switch to generate tab to show results
        $this->activeTab = 'generate';

        session()->flash('success', 'Commission report generated successfully!');
    }

    public function previewSales()
    {
        // Get all unprocessed sales with user information
        $sales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->select(
                    'acs_sales.*',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id'
                )
                ->orderBy('acs_sales.created_at', 'desc')
                ->get();

        if ($sales->isEmpty()) {
            session()->flash('error', 'No unprocessed sales found to preview.');
            return;
        }

        $previewData = [];
        $totalPotentialCommission = 0;
        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;

        // Group sales by user and variety_type for commission calculation
        $userSalesGrouped = $sales->groupBy(function($sale) {
            return $sale->acs_users_id . '_' . $sale->variety_type;
        });

        foreach ($sales as $sale) {
            // Calculate quantity for this user's variety type
            $userVarietyKey = $sale->acs_users_id . '_' . $sale->variety_type;
            $quantity = $userSalesGrouped[$userVarietyKey]->count();

            // Find the commission tier for this sale
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $sale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($sale->total_price * $commissionPercentage) / 100;
            }

            // Commission distribution calculations (40/60 split)
            $agentCommission = ($commissionAmount * 60) / 100;
            $masterAgentCommission = ($commissionAmount * 40) / 100;

            $totalPotentialCommission += $commissionAmount;
            $totalMainAgentPayout += $masterAgentCommission;
            $totalAgentPayout += $agentCommission;

            $previewData[] = [
                'sale_id' => $sale->id,
                'user_name' => $sale->user_name,
                'user_email' => $sale->user_email,
                'cooperative_id' => $sale->acs_coorperative_id,
                'variety_type' => $sale->variety_type,
                'sku_number' => $sale->sku_number,
                'total_price' => $sale->total_price,
                'quantity_for_tier' => $quantity,
                'commission_percentage' => $commissionPercentage,
                'commission_amount' => $commissionAmount,
                'agent_commission' => $agentCommission,
                'master_agent_commission' => $masterAgentCommission,
                'tier_found' => $tierCommission ? true : false,
                'created_at' => $sale->created_at
            ];
        }

        $this->previewData = [
            'sales' => $previewData,
            'summary' => [
                'total_sales' => count($sales),
                'total_potential_commission' => $totalPotentialCommission,
                'total_main_agent_payout' => $totalMainAgentPayout,
                'total_agent_payout' => $totalAgentPayout,
            ]
        ];

        $this->showPreview = true;
        $this->activeTab = 'preview';
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        if ($tab !== 'preview') {
            $this->showPreview = false;
        }
        $this->resetPage();
    }

    public function updatedSearchTerm()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedSalesFilter()
    {
        $this->resetPage();
    }

    public function getCommissionHistoryProperty()
    {
        $query = AcsSalesCommission::with(['distributions.mainAgent', 'distributions.agent'])
            ->orderBy('created_at', 'desc');

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('invoice_no', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('affiliate_membership_no', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filters
        if ($this->dateFrom) {
            $query->whereDate('created_at', '>=', $this->dateFrom);
        }

        if ($this->dateTo) {
            $query->whereDate('created_at', '<=', $this->dateTo);
        }

        return $query->paginate(10);
    }

    public function getHistoryStatsProperty()
    {
        $stats = AcsSalesCommission::selectRaw('
            COUNT(*) as total_records,
            SUM(comission_amount) as total_commission,
            AVG(comission_amount) as avg_commission,
            MAX(comission_amount) as max_commission
        ')->first();

        $distributionStats = AcsCommissionDistribution::selectRaw('
            SUM(main_agent_commission_amount) as total_main_agent,
            SUM(agent_commission_amount) as total_agent
        ')->first();

        return [
            'total_records' => $stats->total_records ?? 0,
            'total_commission' => $stats->total_commission ?? 0,
            'avg_commission' => $stats->avg_commission ?? 0,
            'max_commission' => $stats->max_commission ?? 0,
            'total_main_agent' => $distributionStats->total_main_agent ?? 0,
            'total_agent' => $distributionStats->total_agent ?? 0,
        ];
    }

    public function getSalesListProperty()
    {
        $query = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
            ->select(
                'acs_sales.acs_users_id',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_users.acs_coorperative_id',
                'acs_sales.variety_type',
                DB::raw('COUNT(*) as quantity'),
                DB::raw('SUM(acs_sales.total_price) as total_price'),
                DB::raw('MAX(acs_sales.status) as status'), // If any sale is processed (1), show as processed
                DB::raw('GROUP_CONCAT(DISTINCT acs_sales.sku_number SEPARATOR ", ") as sku_numbers'),
                DB::raw('MIN(acs_sales.created_at) as first_sale_date'),
                DB::raw('MAX(acs_sales.created_at) as last_sale_date')
            )
            ->groupBy(
                'acs_sales.acs_users_id',
                'acs_users.name',
                'acs_users.email',
                'acs_users.acs_coorperative_id',
                'acs_sales.variety_type'
            )
            ->orderBy('last_sale_date', 'desc');

        // Apply status filter
        if ($this->salesFilter === 'processed') {
            $query->havingRaw('MAX(acs_sales.status) = 1');
        } elseif ($this->salesFilter === 'unprocessed') {
            $query->havingRaw('MAX(acs_sales.status) = 0');
        }

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('acs_users.name', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_users.email', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_sales.variety_type', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filters
        if ($this->dateFrom) {
            $query->whereDate('acs_sales.created_at', '>=', $this->dateFrom);
        }

        if ($this->dateTo) {
            $query->whereDate('acs_sales.created_at', '<=', $this->dateTo);
        }

        return $query->paginate(15);
    }

    public function getSalesStatsProperty()
    {
        $totalSales = DB::table('acs_sales')->count();
        $processedSales = DB::table('acs_sales')->where('status', 1)->count();
        $unprocessedSales = DB::table('acs_sales')->where('status', 0)->count();
        $totalValue = DB::table('acs_sales')->sum('total_price');

        return [
            'total_sales' => $totalSales,
            'processed_sales' => $processedSales,
            'unprocessed_sales' => $unprocessedSales,
            'total_value' => $totalValue,
        ];
    }

    public function calculateSaleCommission($sale)
    {
        // Use the quantity from the grouped data (already calculated)
        $quantity = $sale->quantity;

        // Find the commission tier
        $tierCommission = DB::table('acs_commission_tiers')
            ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
            ->where('acs_comission_settings.variety_type', $sale->variety_type)
            ->where('acs_commission_tiers.min_qty', '<=', $quantity)
            ->where('acs_commission_tiers.max_qty', '>=', $quantity)
            ->where('acs_commission_tiers.deleted_at', null)
            ->select('acs_commission_tiers.commission_percentage')
            ->first();

        $commissionPercentage = $tierCommission ? $tierCommission->commission_percentage : 0;
        $commissionAmount = ($sale->total_price * $commissionPercentage) / 100;
        $agentCommission = ($commissionAmount * 60) / 100;
        $masterAgentCommission = ($commissionAmount * 40) / 100;

        return [
            'quantity_for_tier' => $quantity,
            'commission_percentage' => $commissionPercentage,
            'commission_amount' => $commissionAmount,
            'agent_commission' => $agentCommission,
            'master_agent_commission' => $masterAgentCommission,
            'tier_found' => $tierCommission ? true : false
        ];
    }

    /**
     * Alternative implementation using Eloquent models for better performance
     */
    public function generateWithEloquent(){
        // Get aggregated sales data grouped by user using Eloquent
        $userSales = AcsSales::with('user')
                ->select(
                    'acs_users_id',
                    'variety_type',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(total_price) as total_price')
                )
                ->groupBy('acs_users_id', 'variety_type')
                ->get();

        $commissionReports = [];

        foreach($userSales as $userSale){
            // Find the commission setting for this user's total quantity
            $commissionSetting = AcsCommissionSetting::with('tiers')
                ->where('variety_type', $userSale->variety_type)
                ->where('status', 'active')
                ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;
            $tierFound = false;

            if ($commissionSetting) {
                // Use the model method to get commission for quantity
                $commissionPercentage = $commissionSetting->getCommissionForQuantity($userSale->quantity);

                if ($commissionPercentage !== null) {
                    $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
                    $tierFound = true;
                }
            }

            // Add to commission reports
            $commissionReports[] = [
                'acs_user_id' => $userSale->acs_users_id,
                'user_name' => $userSale->user->name ?? 'Unknown',
                'user_email' => $userSale->user->email ?? 'Unknown',
                'cooperative_id' => $userSale->user->acs_coorperative_id ?? null,
                'quantity' => $userSale->quantity,
                'total_price' => $userSale->total_price,
                'variety_type' => $userSale->variety_type,
                'commission_percentage' => $commissionPercentage ?? 0,
                'commission_amount' => $commissionAmount,
                'tier_found' => $tierFound
            ];
        }

        // For now, just dump the results for testing
        dd($commissionReports);
    }
}
